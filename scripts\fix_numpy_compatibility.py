#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NumPy Compatibility Fix Script / NumPy兼容性修复脚本

This script fixes NumPy 2.x compatibility issues with FAISS.
该脚本修复NumPy 2.x与FAISS的兼容性问题。

FAISS libraries are compiled with NumPy 1.x and cannot run with NumPy 2.x.
This script downgrades NumPy to a compatible version.

FAISS库是用NumPy 1.x编译的，无法在NumPy 2.x上运行。
该脚本将NumPy降级到兼容版本。

Usage / 使用方法:
  python scripts/fix_numpy_compatibility.py
"""

import subprocess
import sys
import os
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

console = Console()

def check_numpy_version():
    """检查当前NumPy版本"""
    try:
        import numpy as np
        return np.__version__
    except ImportError:
        return None

def test_faiss_import():
    """测试FAISS导入"""
    try:
        import faiss
        return True, faiss.__version__
    except ImportError as e:
        return False, str(e)

def fix_numpy_compatibility():
    """修复NumPy兼容性问题"""
    console.print(Panel.fit(
        "[bold blue]NumPy Compatibility Fix for FAISS[/bold blue]\n"
        "[yellow]修复FAISS的NumPy兼容性问题[/yellow]",
        border_style="blue"
    ))
    
    # 检查当前NumPy版本
    numpy_version = check_numpy_version()
    if numpy_version:
        console.print(f"🔍 Current NumPy version: [bold]{numpy_version}[/bold]")
        
        if numpy_version.startswith("2."):
            console.print("[red]⚠️  NumPy 2.x detected - FAISS compatibility issue![/red]")
            
            # 测试FAISS导入
            faiss_works, faiss_info = test_faiss_import()
            if faiss_works:
                console.print(f"✅ FAISS is working fine with current NumPy: {faiss_info}")
                return True
            else:
                console.print(f"❌ FAISS import failed: {faiss_info}")
        else:
            console.print("✅ NumPy version is compatible with FAISS")
            # 仍然测试FAISS导入
            faiss_works, faiss_info = test_faiss_import()
            if faiss_works:
                console.print(f"✅ FAISS is working: {faiss_info}")
                return True
            else:
                console.print(f"❌ FAISS import failed: {faiss_info}")
    else:
        console.print("ℹ️  NumPy not installed")
    
    # 询问用户是否要修复
    console.print("\n[yellow]This will:[/yellow]")
    console.print("1. Uninstall current NumPy")
    console.print("2. Install NumPy 1.x (compatible with FAISS)")
    console.print("3. Test FAISS import")
    
    response = input("\nProceed with NumPy compatibility fix? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        console.print("❌ Operation cancelled")
        return False
    
    try:
        # 步骤1：卸载当前NumPy
        console.print("\n🔄 Step 1: Uninstalling current NumPy...")
        subprocess.run([
            sys.executable, "-m", "pip", "uninstall", "numpy", "-y"
        ], check=True, capture_output=True, text=True)
        console.print("✅ NumPy uninstalled")
        
        # 步骤2：安装兼容版本
        console.print("🔄 Step 2: Installing compatible NumPy version...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", "numpy>=1.24.0,<2.0.0"
        ], check=True, capture_output=True, text=True)
        console.print("✅ Compatible NumPy installed")
        
        # 步骤3：验证安装
        console.print("🔄 Step 3: Verifying installation...")
        new_numpy_version = check_numpy_version()
        if new_numpy_version:
            console.print(f"✅ New NumPy version: [bold green]{new_numpy_version}[/bold green]")
        else:
            console.print("❌ NumPy installation verification failed")
            return False
        
        # 步骤4：测试FAISS
        console.print("🔄 Step 4: Testing FAISS import...")
        faiss_works, faiss_info = test_faiss_import()
        if faiss_works:
            console.print(f"✅ FAISS is now working: [bold green]{faiss_info}[/bold green]")
            console.print("\n🎉 [bold green]NumPy compatibility fix completed successfully![/bold green]")
            return True
        else:
            console.print(f"❌ FAISS still not working: {faiss_info}")
            console.print("\n💡 [yellow]You may need to reinstall FAISS:[/yellow]")
            console.print("   pip uninstall faiss-cpu faiss-gpu -y")
            console.print("   pip install faiss-cpu")
            return False
            
    except subprocess.CalledProcessError as e:
        console.print(f"❌ Error during fix: {e}")
        return False
    except Exception as e:
        console.print(f"❌ Unexpected error: {e}")
        return False

def main():
    """主函数"""
    # 确保在正确的目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    console.print(f"📁 Working directory: [bold]{project_root}[/bold]")
    
    success = fix_numpy_compatibility()
    
    if success:
        console.print("\n📋 [bold green]Next steps:[/bold green]")
        console.print("1. Run: [bold]python scripts/01_setup_and_test.py[/bold]")
        console.print("2. If issues persist, reinstall dependencies:")
        console.print("   [bold]python scripts/00_install_dependencies.py[/bold]")
    else:
        console.print("\n📋 [bold yellow]Troubleshooting:[/bold yellow]")
        console.print("1. Try reinstalling FAISS:")
        console.print("   [bold]pip uninstall faiss-cpu faiss-gpu -y[/bold]")
        console.print("   [bold]pip install faiss-cpu[/bold]")
        console.print("2. Run full dependency installation:")
        console.print("   [bold]python scripts/00_install_dependencies.py[/bold]")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
