#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen3 Models Fix Script / Qwen3模型修复脚本

This script fixes Qwen3 model loading issues on Linux servers.
该脚本修复Linux服务器上的Qwen3模型加载问题。

Common issues fixed:
- "argument of type 'NoneType' is not iterable" error
- SentenceTransformer compatibility issues
- Flash Attention compatibility problems
- Environment variable configuration

修复的常见问题：
- "argument of type 'NoneType' is not iterable" 错误
- SentenceTransformer兼容性问题
- Flash Attention兼容性问题
- 环境变量配置

Usage / 使用方法:
  python fix_qwen3_models.py
"""

import os
import sys
import subprocess
from pathlib import Path

def set_environment_variables():
    """Set environment variables to fix common issues / 设置环境变量修复常见问题"""
    print("🔧 Setting environment variables...")
    
    env_vars = {
        "TOKENIZERS_PARALLELISM": "false",
        "HF_HUB_DISABLE_PROGRESS_BARS": "1",
        "TRANSFORMERS_OFFLINE": "0",
        "HF_HUB_OFFLINE": "0",
        "CUDA_VISIBLE_DEVICES": "0"  # Use first GPU only
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"   {key}={value}")
    
    print("✅ Environment variables set")

def test_transformers_import():
    """Test if transformers can be imported without errors / 测试transformers是否可以无错误导入"""
    print("\n🧪 Testing transformers import...")
    
    try:
        from transformers import AutoTokenizer, AutoModel
        print("✅ Transformers import successful")
        return True
    except Exception as e:
        print(f"❌ Transformers import failed: {e}")
        return False

def test_qwen3_tokenizer():
    """Test Qwen3 tokenizer loading / 测试Qwen3分词器加载"""
    print("\n🧪 Testing Qwen3 tokenizer...")
    
    try:
        from transformers import AutoTokenizer
        
        tokenizer = AutoTokenizer.from_pretrained(
            "Qwen/Qwen3-Embedding-0.6B",
            padding_side='left',
            trust_remote_code=True,
            use_fast=False
        )
        print("✅ Qwen3 tokenizer loaded successfully")
        return True
    except Exception as e:
        print(f"❌ Qwen3 tokenizer loading failed: {e}")
        return False

def test_qwen3_model():
    """Test Qwen3 model loading / 测试Qwen3模型加载"""
    print("\n🧪 Testing Qwen3 model...")
    
    try:
        from transformers import AutoModel
        import torch
        
        model_kwargs = {
            "trust_remote_code": True,
            "attn_implementation": "eager",
            "low_cpu_mem_usage": True
        }
        
        if torch.cuda.is_available():
            model_kwargs["device_map"] = "auto"
            model_kwargs["torch_dtype"] = torch.float16
        else:
            model_kwargs["torch_dtype"] = torch.float32
        
        model = AutoModel.from_pretrained(
            "Qwen/Qwen3-Embedding-0.6B",
            **model_kwargs
        )
        print("✅ Qwen3 model loaded successfully")
        return True
    except Exception as e:
        print(f"❌ Qwen3 model loading failed: {e}")
        return False

def test_sentence_transformers():
    """Test SentenceTransformers with Qwen3 / 测试SentenceTransformers与Qwen3"""
    print("\n🧪 Testing SentenceTransformers with Qwen3...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        model_kwargs = {
            "trust_remote_code": True,
            "attn_implementation": "eager"
        }
        
        model = SentenceTransformer(
            "Qwen/Qwen3-Embedding-0.6B",
            model_kwargs=model_kwargs,
            tokenizer_kwargs={"padding_side": "left", "trust_remote_code": True}
        )
        
        # Test encoding
        texts = ["Hello world", "Test sentence"]
        embeddings = model.encode(texts)
        print(f"✅ SentenceTransformers working - embeddings shape: {embeddings.shape}")
        return True
    except Exception as e:
        print(f"❌ SentenceTransformers test failed: {e}")
        return False

def update_transformers():
    """Update transformers to latest version / 更新transformers到最新版本"""
    print("\n🔄 Updating transformers...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "transformers>=4.51.0", "--upgrade"
        ], check=True, capture_output=True, text=True)
        print("✅ Transformers updated successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to update transformers: {e}")
        return False

def clear_cache():
    """Clear HuggingFace cache / 清除HuggingFace缓存"""
    print("\n🧹 Clearing HuggingFace cache...")
    
    try:
        import shutil
        from pathlib import Path
        
        # Common cache locations
        cache_dirs = [
            Path.home() / ".cache" / "huggingface",
            Path.home() / ".cache" / "torch",
            Path("/tmp/huggingface_cache")
        ]
        
        for cache_dir in cache_dirs:
            if cache_dir.exists():
                shutil.rmtree(cache_dir, ignore_errors=True)
                print(f"   Cleared: {cache_dir}")
        
        print("✅ Cache cleared")
        return True
    except Exception as e:
        print(f"⚠️  Cache clearing failed: {e}")
        return False

def main():
    """Main fix process / 主修复流程"""
    print("🚀 Qwen3 Models Fix Script")
    print("=" * 50)
    
    # Set environment variables
    set_environment_variables()
    
    # Test current state
    tests = [
        ("Transformers Import", test_transformers_import),
        ("Qwen3 Tokenizer", test_qwen3_tokenizer),
        ("Qwen3 Model", test_qwen3_model),
        ("SentenceTransformers", test_sentence_transformers)
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # If any tests failed, try fixes
    if not all(results.values()):
        print("\n🔧 Some tests failed, applying fixes...")
        
        # Try updating transformers
        if not results["Transformers Import"]:
            update_transformers()
        
        # Clear cache if model loading failed
        if not results["Qwen3 Model"]:
            clear_cache()
        
        # Re-run tests
        print("\n🔄 Re-running tests after fixes...")
        for test_name, test_func in tests:
            results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("\n🎉 All tests passed! Qwen3 models should work correctly.")
        print("\n📋 Next steps:")
        print("1. Run: python scripts/01_setup_and_test.py")
        print("2. If successful, continue with: python scripts/02_download_data.py")
        return 0
    else:
        print("\n⚠️  Some tests still failing. Manual intervention may be required.")
        print("\n💡 Troubleshooting:")
        print("1. Check internet connection")
        print("2. Verify GPU memory availability")
        print("3. Try: pip install transformers sentence-transformers --upgrade")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
