# Logging Configuration Guide / 日志配置指南

## 概述 / Overview

本文档描述了GRAG项目的日志配置系统，确保所有日志文件都写入项目根目录的`logs`文件夹，而不是在scripts或其他子目录中创建日志文件。

This document describes the GRAG project's logging configuration system, ensuring all log files are written to the project root `logs` directory rather than creating log files in scripts or other subdirectories.

## 问题背景 / Background

### 原始问题 / Original Problem
- 当从`scripts`目录运行脚本时，日志系统会在`scripts/logs`目录创建日志文件
- 这导致日志文件分散在多个位置，难以管理和查找
- 项目根目录已经有一个`logs`目录，应该统一使用

### 解决方案 / Solution
- 修改日志管理器，使其始终将日志写入项目根目录的`logs`文件夹
- 实现项目根目录自动检测功能
- 确保无论从哪个目录运行脚本，日志都写入正确位置

## 技术实现 / Technical Implementation

### 1. 项目根目录检测 / Project Root Detection

```python
def find_project_root() -> Path:
    """
    Find the project root directory / 查找项目根目录
    
    Returns:
        Path to project root / 项目根目录路径
    """
    current = Path(__file__).parent
    while current != current.parent:
        # Look for config directory or other project markers
        if (current / "configs").exists() or (current / "logs").exists() or (current / "src").exists():
            return current
        current = current.parent
    
    # Fallback: use current working directory
    return Path.cwd()
```

### 2. 日志路径解析 / Log Path Resolution

```python
# If path is relative, make it relative to project root
if not Path(log_file_path).is_absolute():
    project_root = find_project_root()
    log_file = project_root / log_file_path
else:
    log_file = Path(log_file_path)
```

### 3. 配置文件设置 / Configuration File Settings

**config.yaml:**
```yaml
# 日志配置
logging:
  level: INFO  # 日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
  file: ./logs/grag.log  # 日志文件路径（相对于项目根目录）
  format: '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}'
  rotation: 1 day  # 日志轮转周期
  retention: 30 days  # 日志保留时间
```

## 目录结构 / Directory Structure

### ✅ 正确的日志结构 / Correct Log Structure
```
GRAG/
├── logs/                    # ✅ 所有日志文件都在这里
│   └── grag.log            # ✅ 主日志文件
├── scripts/                # ✅ 脚本目录（无logs子目录）
│   ├── 01_setup_and_test.py
│   ├── 04_train_sft.py
│   └── ...
├── src/
├── configs/
└── ...
```

### ❌ 错误的日志结构 / Incorrect Log Structure
```
GRAG/
├── logs/
│   └── grag.log
├── scripts/
│   ├── logs/               # ❌ 不应该存在
│   │   └── grag.log        # ❌ 重复的日志文件
│   └── ...
└── ...
```

## 使用方法 / Usage

### 1. 基本使用 / Basic Usage

```python
# 从任何位置导入logger
from utils import get_logger

# 创建logger实例
logger = get_logger("my_module")

# 记录日志
logger.info("This message will go to project_root/logs/grag.log")
logger.warning("Warning message")
logger.error("Error message")
```

### 2. 从scripts目录运行 / Running from Scripts Directory

```bash
# 从scripts目录运行脚本
cd scripts
python 04_train_sft.py

# 日志仍然会写入 ../logs/grag.log
```

### 3. 从项目根目录运行 / Running from Project Root

```bash
# 从项目根目录运行脚本
python scripts/04_train_sft.py

# 日志会写入 ./logs/grag.log
```

## 测试和验证 / Testing and Verification

### 1. 运行日志位置测试 / Run Log Location Test

```bash
cd scripts
python test_logging_location.py
```

**预期输出：**
```
✅ PASS: Logs are being written to project root logs directory
✅ PASS: No logs directory created in scripts
```

### 2. 清理错误位置的日志 / Clean Up Misplaced Logs

```bash
cd scripts
python cleanup_logs.py
```

这个脚本会：
- 检查并清理scripts目录中的logs文件夹
- 将错误位置的日志文件移动到正确位置
- 测试日志配置是否正常工作

## 配置选项 / Configuration Options

### 日志级别 / Log Levels
- `DEBUG`: 详细的调试信息
- `INFO`: 一般信息（默认）
- `WARNING`: 警告信息
- `ERROR`: 错误信息
- `CRITICAL`: 严重错误

### 日志轮转 / Log Rotation
- `1 day`: 每天轮转（默认）
- `1 week`: 每周轮转
- `100 MB`: 按文件大小轮转

### 日志保留 / Log Retention
- `30 days`: 保留30天（默认）
- `7 days`: 保留7天
- `3 months`: 保留3个月

## 故障排除 / Troubleshooting

### 1. 日志文件未创建 / Log File Not Created

**检查步骤：**
```bash
# 检查项目根目录
ls -la logs/

# 检查权限
ls -ld logs/

# 测试日志配置
python scripts/test_logging_location.py
```

### 2. 日志写入错误位置 / Logs Written to Wrong Location

**解决方案：**
```bash
# 运行清理脚本
python scripts/cleanup_logs.py

# 重新测试
python scripts/test_logging_location.py
```

### 3. 权限问题 / Permission Issues

**检查和修复：**
```bash
# 检查logs目录权限
ls -ld logs/

# 如果需要，修复权限
chmod 755 logs/
chmod 644 logs/*.log
```

## 最佳实践 / Best Practices

### 1. 日志记录 / Logging Practices

```python
# ✅ 好的做法
logger = get_logger(__name__)  # 使用模块名
logger.info("Processing started")
logger.error(f"Error processing file: {filename}")

# ❌ 避免的做法
print("Debug message")  # 使用logger而不是print
logger.info("Debug info")  # 使用适当的日志级别
```

### 2. 日志格式 / Log Format

```python
# 日志会自动包含：
# - 时间戳
# - 日志级别
# - 模块名:函数名:行号
# - 消息内容

# 示例输出：
# 2025-06-10 11:31:13 | INFO | training.sft_trainer:train:142 - Starting SFT training...
```

### 3. 性能考虑 / Performance Considerations

```python
# ✅ 高效的日志记录
if logger.isEnabledFor(logging.DEBUG):
    logger.debug(f"Complex calculation result: {expensive_operation()}")

# ✅ 使用f-string
logger.info(f"Processing file {filename}")

# ❌ 避免不必要的字符串格式化
logger.debug("Result: " + str(expensive_operation()))  # 即使DEBUG关闭也会执行
```

## 更新日志 / Changelog

### v1.1.0 (2025-06-10)
- ✅ 实现项目根目录自动检测
- ✅ 修复日志路径解析逻辑
- ✅ 确保所有日志写入项目根目录logs文件夹
- ✅ 添加日志位置测试脚本
- ✅ 添加日志清理工具

### v1.0.0
- ✅ 初始日志配置系统
- ✅ 基于loguru的彩色日志输出
- ✅ 文件和控制台双重输出

## 参考资料 / References

- [Loguru Documentation](https://loguru.readthedocs.io/)
- [Python Logging Best Practices](https://docs.python.org/3/howto/logging.html)
- [GRAG Project Structure](../README.md)
