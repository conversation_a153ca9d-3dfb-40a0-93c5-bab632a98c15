# -*- coding: utf-8 -*-
"""
Logging Module / 日志模块

This module provides centralized logging functionality for the GRAG system.
该模块为GRAG系统提供集中式日志功能。
"""

import sys
import os
from pathlib import Path
from loguru import logger
from typing import Optional
from .config import get_config_manager


def find_project_root() -> Path:
    """
    Find the project root directory / 查找项目根目录

    Returns:
        Path to project root / 项目根目录路径
    """
    current = Path(__file__).parent
    while current != current.parent:
        # Look for config directory or other project markers
        if (current / "configs").exists() or (current / "logs").exists() or (current / "src").exists():
            return current
        current = current.parent

    # Fallback: use current working directory
    return Path.cwd()


class LoggerManager:
    """Logger manager / 日志管理器"""
    
    def __init__(self):
        """Initialize logger manager / 初始化日志管理器"""
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logger configuration / 设置日志配置"""
        # Remove default handler / 移除默认处理器
        logger.remove()

        # Get logging configuration / 获取日志配置
        try:
            config_manager = get_config_manager()
            log_config = config_manager.logging_config
        except Exception:
            # Fallback to default configuration if config loading fails
            # 如果配置加载失败，回退到默认配置
            self._setup_default_logger()
            return
        
        # Ensure log directory exists / 确保日志目录存在
        log_file_path = log_config.file

        # If path is relative, make it relative to project root / 如果路径是相对的，使其相对于项目根目录
        if not Path(log_file_path).is_absolute():
            project_root = find_project_root()
            log_file = project_root / log_file_path
        else:
            log_file = Path(log_file_path)

        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Add console handler / 添加控制台处理器
        logger.add(
            sys.stdout,
            level=log_config.level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                   "<level>{message}</level>",
            colorize=True
        )
        
        # Add file handler / 添加文件处理器
        logger.add(
            str(log_file),  # Use the resolved log file path / 使用解析后的日志文件路径
            level=log_config.level,
            format=log_config.format,
            rotation=log_config.rotation,
            retention=log_config.retention,
            encoding="utf-8"
        )

    def _setup_default_logger(self):
        """Setup default logger configuration / 设置默认日志配置"""
        # Add console handler with default settings / 添加默认设置的控制台处理器
        logger.add(
            sys.stdout,
            level="INFO",
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                   "<level>{message}</level>",
            colorize=True
        )

        # Add file handler with default settings / 添加默认设置的文件处理器
        project_root = find_project_root()
        log_file = project_root / "logs" / "grag.log"
        log_file.parent.mkdir(parents=True, exist_ok=True)

        logger.add(
            str(log_file),
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}",
            rotation="1 day",
            retention="30 days",
            encoding="utf-8"
        )
    
    def get_logger(self, name: Optional[str] = None):
        """
        Get logger instance / 获取日志实例
        
        Args:
            name: Logger name / 日志器名称
            
        Returns:
            Logger instance / 日志实例
        """
        if name:
            return logger.bind(name=name)
        return logger


# Global logger manager / 全局日志管理器
logger_manager = LoggerManager()

# Convenience function to get logger / 获取日志器的便利函数
def get_logger(name: Optional[str] = None):
    """
    Get logger instance / 获取日志实例
    
    Args:
        name: Logger name / 日志器名称
        
    Returns:
        Logger instance / 日志实例
    """
    return logger_manager.get_logger(name)


# Export logger for direct use / 导出日志器供直接使用
log = get_logger("GRAG")
