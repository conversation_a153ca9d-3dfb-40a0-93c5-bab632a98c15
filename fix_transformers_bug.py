#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Transformers NoneType Bug Fix / Transformers NoneType错误修复

This script fixes the "argument of type 'NoneType' is not iterable" error
in transformers library when loading Qwen3 models.

该脚本修复在加载Qwen3模型时transformers库中的
"argument of type 'NoneType' is not iterable" 错误。

Usage / 使用方法:
  python fix_transformers_bug.py
"""

import os
import sys
import importlib.util

def patch_transformers():
    """Patch transformers to fix NoneType error / 修补transformers修复NoneType错误"""
    print("🔧 Patching transformers library...")
    
    try:
        import transformers
        from transformers import modeling_utils
        
        # Get the original post_init method
        original_post_init = modeling_utils.PreTrainedModel.post_init
        
        def patched_post_init(self):
            """Patched post_init method that handles None values / 处理None值的修补post_init方法"""
            try:
                # Set default values for None attributes
                if not hasattr(self.config, 'fsdp_config') or self.config.fsdp_config is None:
                    self.config.fsdp_config = {}
                
                # Call original post_init
                return original_post_init(self)
            except TypeError as e:
                if "argument of type 'NoneType' is not iterable" in str(e):
                    print("   ⚠️  Caught NoneType error, applying fix...")
                    # Set safe defaults
                    if hasattr(self.config, 'fsdp_config'):
                        if self.config.fsdp_config is None:
                            self.config.fsdp_config = {}
                    return
                else:
                    raise e
        
        # Apply the patch
        modeling_utils.PreTrainedModel.post_init = patched_post_init
        print("✅ Transformers patched successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to patch transformers: {e}")
        return False

def test_qwen3_with_patch():
    """Test Qwen3 loading with the patch / 使用补丁测试Qwen3加载"""
    print("\n🧪 Testing Qwen3 with patch...")
    
    try:
        from transformers import AutoTokenizer, AutoModel
        import torch
        
        # Set environment variables
        os.environ["TOKENIZERS_PARALLELISM"] = "false"
        
        model_name = "Qwen/Qwen3-Embedding-0.6B"
        
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            trust_remote_code=True,
            use_fast=False
        )
        print("✅ Tokenizer loaded")
        
        # Load model with patch
        model = AutoModel.from_pretrained(
            model_name,
            trust_remote_code=True,
            attn_implementation="eager",
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            device_map="auto" if torch.cuda.is_available() else None,
            low_cpu_mem_usage=True
        )
        print("✅ Model loaded successfully with patch")
        
        # Test encoding
        texts = ["Hello world", "Test sentence"]
        inputs = tokenizer(texts, padding=True, truncation=True, return_tensors="pt")
        
        if torch.cuda.is_available():
            inputs = {k: v.to(model.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = model(**inputs)
            embeddings = outputs.last_hidden_state.mean(dim=1)
        
        print(f"✅ Encoding successful - shape: {embeddings.shape}")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def create_permanent_fix():
    """Create a permanent fix file / 创建永久修复文件"""
    print("\n🔧 Creating permanent fix...")
    
    fix_code = '''
import os
import sys

# Set environment variables to prevent issues
os.environ.setdefault("TOKENIZERS_PARALLELISM", "false")
os.environ.setdefault("HF_HUB_DISABLE_PROGRESS_BARS", "1")

def patch_transformers_for_qwen3():
    """Patch transformers for Qwen3 compatibility"""
    try:
        import transformers
        from transformers import modeling_utils
        
        original_post_init = modeling_utils.PreTrainedModel.post_init
        
        def safe_post_init(self):
            try:
                if hasattr(self.config, 'fsdp_config') and self.config.fsdp_config is None:
                    self.config.fsdp_config = {}
                return original_post_init(self)
            except TypeError as e:
                if "argument of type 'NoneType' is not iterable" in str(e):
                    if hasattr(self.config, 'fsdp_config'):
                        if self.config.fsdp_config is None:
                            self.config.fsdp_config = {}
                    return
                raise e
        
        modeling_utils.PreTrainedModel.post_init = safe_post_init
        return True
    except:
        return False

# Apply patch automatically
patch_transformers_for_qwen3()
'''
    
    try:
        with open("qwen3_patch.py", "w", encoding="utf-8") as f:
            f.write(fix_code)
        
        print("✅ Permanent fix created: qwen3_patch.py")
        print("   Import this file before using Qwen3 models:")
        print("   import qwen3_patch")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create permanent fix: {e}")
        return False

def main():
    """Main fix process / 主修复流程"""
    print("🚀 Transformers NoneType Bug Fix")
    print("=" * 40)
    
    # Apply patch
    if not patch_transformers():
        print("❌ Failed to apply patch")
        return 1
    
    # Test with patch
    if test_qwen3_with_patch():
        print("\n🎉 Patch successful! Qwen3 models working.")
        
        # Create permanent fix
        create_permanent_fix()
        
        print("\n📋 Next steps:")
        print("1. Add 'import qwen3_patch' to your scripts")
        print("2. Run: python scripts/01_setup_and_test.py")
        return 0
    else:
        print("\n❌ Patch failed. Try alternative solutions.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
