# GRAG 模型兼容性修复指南 / GRAG Model Compatibility Fix Guide

## 问题描述 / Problem Description

在Linux服务器上运行 `python scripts/01_setup_and_test.py` 时可能出现以下错误：

### 1. NumPy兼容性问题
```
A module that was compiled using NumPy 1.x cannot be run in
NumPy 2.3.0 as it may crash. To support both 1.x and 2.x
versions of NumPy, modules must be compiled with NumPy 2.0.
```

### 2. Qwen3模型加载问题
```
argument of type 'NoneType' is not iterable
Failed to load with specific configuration
SentenceTransformer loading failed
```

### 3. Flash Attention兼容性问题
```
FlashAttention2 has been toggled on, but it cannot be used due to the following error: 
the package flash_attn seems to be not installed.
```

## 解决方案 / Solutions

### 🚀 一键修复脚本 (推荐)

#### 方案1：完整修复
```bash
python fix_qwen3_models.py
```

#### 方案2：简单测试
```bash
python test_qwen3_simple.py
```

### 🔧 分步修复

#### 步骤1：修复NumPy兼容性
```bash
# 卸载当前NumPy
pip uninstall numpy -y

# 安装兼容版本
pip install "numpy>=1.24.0,<2.0.0"

# 重新安装FAISS
pip uninstall faiss-cpu faiss-gpu -y
pip install faiss-cpu
```

#### 步骤2：修复Qwen3模型加载
```bash
# 设置环境变量
export TOKENIZERS_PARALLELISM=false
export HF_HUB_DISABLE_PROGRESS_BARS=1

# 更新transformers
pip install "transformers>=4.51.0" --upgrade

# 清除缓存
rm -rf ~/.cache/huggingface
```

#### 步骤3：修复Flash Attention
```bash
# 选项A：安装Flash Attention (如果支持)
pip install flash-attn --no-build-isolation

# 选项B：禁用Flash Attention (推荐)
# 代码已自动配置为使用eager attention
```

### 🛠️ 手动修复步骤

如果自动脚本失败，可以手动执行以下步骤：

1. **检查Python环境**
   ```bash
   python --version  # 应该是3.8+
   pip list | grep torch
   pip list | grep transformers
   ```

2. **重新安装核心依赖**
   ```bash
   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
   pip install "transformers>=4.51.0"
   pip install "sentence-transformers>=2.7.0"
   pip install "numpy>=1.24.0,<2.0.0"
   pip install faiss-cpu
   ```

3. **测试模型加载**
   ```bash
   python -c "
   import torch
   from transformers import AutoTokenizer, AutoModel
   tokenizer = AutoTokenizer.from_pretrained('Qwen/Qwen3-Embedding-0.6B', trust_remote_code=True)
   print('✅ Qwen3 tokenizer loaded successfully')
   "
   ```

## 验证修复 / Verification

修复完成后，运行以下命令验证：

```bash
# 完整测试
python scripts/01_setup_and_test.py

# 简单测试
python test_qwen3_simple.py
```

应该看到：
```
✅ FAISS OK
✅ Qwen3 models working
🎉 All tests passed!
```

## 常见问题 / FAQ

### Q: 为什么会出现 "argument of type 'NoneType' is not iterable" 错误？
A: 这是Transformers库中的一个已知问题，通常由环境变量配置或模型配置问题导致。设置正确的环境变量可以解决。

### Q: Qwen3模型加载很慢怎么办？
A: 首次加载需要下载模型文件，可能需要几分钟。后续加载会使用缓存，速度会快很多。

### Q: 可以使用CPU运行吗？
A: 可以，代码已经配置为自动检测并支持CPU运行。GPU会提供更好的性能。

### Q: Flash Attention是必需的吗？
A: 不是必需的。代码已配置为在Flash Attention不可用时自动使用eager attention。

## 技术细节 / Technical Details

### 修复内容
1. **NumPy版本限制**: 限制为 `<2.0.0` 以兼容FAISS
2. **环境变量设置**: 修复Transformers加载问题
3. **Flash Attention禁用**: 使用eager attention避免兼容性问题
4. **Fallback机制**: 在Qwen3加载失败时自动使用备用模型

### 文件修改
- `src/retrieval/enhanced_vector_store.py`: 增强Qwen3加载逻辑
- `src/retrieval/reranker.py`: 修复reranker初始化
- `requirements.txt`: 限制NumPy版本
- `scripts/00_install_dependencies.py`: 添加兼容性检查

## 支持 / Support

如果问题仍然存在，请：

1. 检查系统要求（Python 3.8+, CUDA 11.8+）
2. 确保网络连接正常（模型下载需要）
3. 查看完整错误日志
4. 尝试在新的虚拟环境中安装

## 更新日志 / Changelog

- **v1.2**: 添加Qwen3模型兼容性修复
- **v1.1**: 修复Flash Attention问题
- **v1.0**: 初始NumPy兼容性修复
