#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen3 GPU Fix Script / Qwen3 GPU修复脚本

This script specifically fixes Qwen3 model loading issues on GPU Linux servers.
该脚本专门修复GPU Linux服务器上的Qwen3模型加载问题。

Issues fixed:
1. "argument of type 'NoneType' is not iterable" error
2. Missing sentence_xlnet_config.json file
3. GPU memory optimization
4. Proper model loading with custom implementation

修复的问题：
1. "argument of type 'NoneType' is not iterable" 错误
2. 缺失的sentence_xlnet_config.json文件
3. GPU内存优化
4. 使用自定义实现正确加载模型

Usage / 使用方法:
  python fix_qwen3_gpu.py
"""

import os
import sys
import json
import torch
from pathlib import Path

def setup_environment():
    """Setup environment variables for GPU / 为GPU设置环境变量"""
    print("🔧 Setting up environment for GPU...")
    
    env_vars = {
        "TOKENIZERS_PARALLELISM": "false",
        "HF_HUB_DISABLE_PROGRESS_BARS": "1",
        "TRANSFORMERS_OFFLINE": "0",
        "HF_HUB_OFFLINE": "0",
        "CUDA_VISIBLE_DEVICES": "0",
        "PYTORCH_CUDA_ALLOC_CONF": "max_split_size_mb:512"
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"   {key}={value}")
    
    print("✅ Environment configured for GPU")

def check_gpu_status():
    """Check GPU status / 检查GPU状态"""
    print("\n🚀 Checking GPU status...")
    
    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        return False
    
    gpu_count = torch.cuda.device_count()
    gpu_name = torch.cuda.get_device_name(0)
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    
    print(f"✅ GPU available: {gpu_name}")
    print(f"   GPU count: {gpu_count}")
    print(f"   GPU memory: {gpu_memory:.1f} GB")
    
    return True

def create_sentence_transformer_config():
    """Create missing SentenceTransformer config for Qwen3 / 为Qwen3创建缺失的SentenceTransformer配置"""
    print("\n🔧 Creating SentenceTransformer config for Qwen3...")
    
    cache_dir = Path.home() / ".cache" / "huggingface" / "hub"
    qwen3_dirs = list(cache_dir.glob("models--Qwen--Qwen3-Embedding-*"))
    
    for model_dir in qwen3_dirs:
        print(f"   Processing: {model_dir.name}")
        
        # Find the actual model directory
        snapshots_dir = model_dir / "snapshots"
        if not snapshots_dir.exists():
            continue
        
        # Get the latest snapshot
        snapshot_dirs = list(snapshots_dir.iterdir())
        if not snapshot_dirs:
            continue
        
        latest_snapshot = max(snapshot_dirs, key=lambda x: x.stat().st_mtime)
        
        # Create sentence transformer config
        config_file = latest_snapshot / "sentence_xlnet_config.json"
        if not config_file.exists():
            config = {
                "do_lower_case": False,
                "remove_duplicates": False,
                "tokenizer_class": "AutoTokenizer",
                "tokenizer_name_or_path": str(latest_snapshot)
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)
            
            print(f"   ✅ Created: {config_file}")
        else:
            print(f"   ℹ️  Already exists: {config_file}")
    
    print("✅ SentenceTransformer configs created")

def test_qwen3_with_custom_implementation():
    """Test Qwen3 with custom implementation / 使用自定义实现测试Qwen3"""
    print("\n🧪 Testing Qwen3 with custom implementation...")
    
    try:
        from transformers import AutoTokenizer, AutoModel
        import torch.nn.functional as F
        
        model_name = "Qwen/Qwen3-Embedding-0.6B"
        
        # Load tokenizer with specific settings
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            padding_side='left',
            trust_remote_code=True,
            use_fast=False,
            local_files_only=False
        )
        print("✅ Tokenizer loaded")
        
        # Load model with GPU optimization
        model = AutoModel.from_pretrained(
            model_name,
            trust_remote_code=True,
            attn_implementation="eager",
            torch_dtype=torch.float16,
            device_map="auto",
            low_cpu_mem_usage=True
        )
        print("✅ Model loaded on GPU")
        
        # Test encoding with custom pooling
        def last_token_pool(last_hidden_states, attention_mask):
            left_padding = (attention_mask[:, -1].sum() == attention_mask.shape[0])
            if left_padding:
                return last_hidden_states[:, -1]
            else:
                sequence_lengths = attention_mask.sum(dim=1) - 1
                batch_size = last_hidden_states.shape[0]
                return last_hidden_states[torch.arange(batch_size, device=last_hidden_states.device), sequence_lengths]
        
        # Test with sample texts
        texts = ["Hello world", "This is a test sentence"]
        max_length = 8192
        
        # Tokenize
        batch_dict = tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=max_length,
            return_tensors="pt",
        )
        
        # Move to GPU
        batch_dict = {k: v.to(model.device) for k, v in batch_dict.items()}
        
        # Get embeddings
        with torch.no_grad():
            outputs = model(**batch_dict)
            embeddings = last_token_pool(outputs.last_hidden_state, batch_dict['attention_mask'])
            embeddings = F.normalize(embeddings, p=2, dim=1)
        
        print(f"✅ Custom implementation working - shape: {embeddings.shape}")
        print(f"   Device: {embeddings.device}")
        print(f"   Dtype: {embeddings.dtype}")
        
        return True
        
    except Exception as e:
        print(f"❌ Custom implementation failed: {e}")
        return False

def test_sentence_transformers_with_config():
    """Test SentenceTransformers after config fix / 配置修复后测试SentenceTransformers"""
    print("\n🧪 Testing SentenceTransformers after config fix...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        model_kwargs = {
            "trust_remote_code": True,
            "attn_implementation": "eager",
            "torch_dtype": torch.float16,
            "device_map": "auto"
        }
        
        tokenizer_kwargs = {
            "padding_side": "left",
            "trust_remote_code": True,
            "use_fast": False
        }
        
        model = SentenceTransformer(
            "Qwen/Qwen3-Embedding-0.6B",
            model_kwargs=model_kwargs,
            tokenizer_kwargs=tokenizer_kwargs
        )
        
        texts = ["Hello world", "This is a test sentence"]
        embeddings = model.encode(texts, show_progress_bar=False)
        
        print(f"✅ SentenceTransformers working - shape: {embeddings.shape}")
        return True
        
    except Exception as e:
        print(f"❌ SentenceTransformers still failed: {e}")
        return False

def optimize_gpu_memory():
    """Optimize GPU memory usage / 优化GPU内存使用"""
    print("\n🔧 Optimizing GPU memory...")
    
    try:
        if torch.cuda.is_available():
            # Clear GPU cache
            torch.cuda.empty_cache()
            
            # Set memory fraction
            torch.cuda.set_per_process_memory_fraction(0.8)
            
            print("✅ GPU memory optimized")
            return True
    except Exception as e:
        print(f"⚠️  GPU memory optimization failed: {e}")
        return False

def main():
    """Main fix process / 主修复流程"""
    print("🚀 Qwen3 GPU Fix Script")
    print("=" * 50)
    
    # Setup environment
    setup_environment()
    
    # Check GPU
    if not check_gpu_status():
        print("❌ GPU not available, exiting...")
        return 1
    
    # Optimize GPU memory
    optimize_gpu_memory()
    
    # Create missing configs
    create_sentence_transformer_config()
    
    # Run tests
    tests = [
        ("Custom Implementation", test_qwen3_with_custom_implementation),
        ("SentenceTransformers", test_sentence_transformers_with_config)
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed >= 1:
        print("\n🎉 Qwen3 models working on GPU!")
        print("\n📋 Next steps:")
        print("1. Run: python scripts/01_setup_and_test.py")
        print("2. The system will use the working implementation")
        return 0
    else:
        print("\n⚠️  All tests failed. Check GPU memory and drivers.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
