# -*- coding: utf-8 -*-
"""
Reranker Module / 重排序模块

This module implements document reranking for improved retrieval quality.
该模块实现文档重排序以提高检索质量。
"""

import torch
from typing import List, Dict, Tuple, Optional, Any
from sentence_transformers import SentenceTransformer, CrossEncoder
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    HAS_TRANSFORMERS = True
except ImportError:
    HAS_TRANSFORMERS = False
from ..utils import get_logger, get_config_manager, calculate_text_similarity, extract_medical_entities

logger = get_logger(__name__)


class MedicalReranker:
    """Medical document reranker / 医疗文档重排序器"""
    
    def __init__(self, reranker_model: str = None):
        """
        Initialize medical reranker / 初始化医疗重排序器

        Args:
            reranker_model: Name of the reranker model / 重排序模型名称
        """
        config_manager = get_config_manager()
        self.reranking_config = config_manager.retrieval.reranking
        self.reranker_model_name = reranker_model or self.reranking_config.get("model_name", "Qwen/Qwen3-Reranker-0.6B")
        self.top_k_rerank = self.reranking_config.get("top_k_rerank", 5)
        self.batch_size = self.reranking_config.get("batch_size", 16)

        # Qwen3特有配置 / Qwen3 specific configuration
        self.use_instruction = self.reranking_config.get("use_instruction", False)
        self.instruction_template = self.reranking_config.get("instruction_template", "Given a web search query, retrieve relevant passages that answer the query")

        self.reranker_model = None
        self.tokenizer = None  # For Qwen3 reranker
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        logger.info(f"Initialized reranker with model: {self.reranker_model_name}")
        if self.use_instruction:
            logger.info(f"Using instruction template: {self.instruction_template}")
    
    def load_reranker_model(self):
        """Load the reranker model / 加载重排序模型"""
        logger.info(f"Loading reranker model: {self.reranker_model_name}")

        try:
            # Check if it's a Qwen3 reranker model / 检查是否为Qwen3重排序模型
            if "qwen3-reranker" in self.reranker_model_name.lower() and HAS_TRANSFORMERS:
                # Load Qwen3 reranker model / 加载Qwen3重排序模型
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.reranker_model_name,
                    padding_side='left',
                    trust_remote_code=True
                )

                model_kwargs = {
                    "trust_remote_code": True,
                    # Disable flash attention to avoid compatibility issues / 禁用flash attention以避免兼容性问题
                    "attn_implementation": "eager"
                }

                if torch.cuda.is_available():
                    model_kwargs["torch_dtype"] = torch.float16
                    model_kwargs["device_map"] = "auto"

                self.reranker_model = AutoModelForCausalLM.from_pretrained(
                    self.reranker_model_name,
                    **model_kwargs
                ).eval()

                # Set up special tokens for Qwen3 reranker / 为Qwen3重排序器设置特殊token
                self.token_false_id = self.tokenizer.convert_tokens_to_ids("no")
                self.token_true_id = self.tokenizer.convert_tokens_to_ids("yes")

                # Set up prompt templates / 设置提示模板
                self.prefix = "<|im_start|>system\nJudge whether the Document meets the requirements based on the Query and the Instruct provided. Note that the answer can only be \"yes\" or \"no\".<|im_end|>\n<|im_start|>user\n"
                self.suffix = "<|im_end|>\n<|im_start|>assistant\n<think>\n\n</think>\n\n"
                self.prefix_tokens = self.tokenizer.encode(self.prefix, add_special_tokens=False)
                self.suffix_tokens = self.tokenizer.encode(self.suffix, add_special_tokens=False)

                logger.info("Loaded Qwen3 reranker model")

            else:
                # Try to load as CrossEncoder first / 首先尝试加载为CrossEncoder
                try:
                    self.reranker_model = CrossEncoder(self.reranker_model_name, device=self.device)
                    logger.info("Loaded CrossEncoder reranker model")
                except Exception as e:
                    logger.warning(f"Failed to load CrossEncoder, using SentenceTransformer: {e}")
                    # Fallback to SentenceTransformer / 回退到SentenceTransformer
                    self.reranker_model = SentenceTransformer(self.reranker_model_name, device=self.device)
                    logger.info("Loaded SentenceTransformer reranker model")

        except Exception as e:
            logger.error(f"Failed to load reranker model: {e}")
            # Final fallback / 最终回退
            try:
                self.reranker_model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2", device=self.device)
                logger.warning("Using fallback reranker model")
            except Exception as e2:
                logger.error(f"Failed to load fallback model: {e2}")
                # Set to None to disable reranking / 设置为None以禁用重排序
                self.reranker_model = None
                logger.warning("Reranking disabled due to model loading failure")
    
    def rerank(self, query: str, documents: List[Dict[str, Any]], 
               top_k: int = None) -> List[Dict[str, Any]]:
        """
        Rerank documents based on relevance to query / 基于与查询的相关性重排序文档
        
        Args:
            query: Search query / 搜索查询
            documents: List of retrieved documents / 检索到的文档列表
            top_k: Number of top documents to return / 返回的顶部文档数量
            
        Returns:
            Reranked documents / 重排序后的文档
        """
        if not documents:
            return documents
        
        if not self.reranking_config.get("enabled", True):
            logger.debug("Reranking disabled, returning original order")
            return documents[:top_k] if top_k else documents
        
        if self.reranker_model is None:
            self.load_reranker_model()
        
        top_k = top_k or self.top_k_rerank
        logger.info(f"Reranking {len(documents)} documents, returning top {top_k}")
        
        # Prepare query-document pairs / 准备查询-文档对
        query_doc_pairs = []
        for doc in documents:
            doc_text = self._extract_document_text(doc)
            query_doc_pairs.append([query, doc_text])
        
        # Get reranking scores / 获取重排序分数
        if "qwen3-reranker" in self.reranker_model_name.lower() and self.tokenizer is not None:
            rerank_scores = self._rerank_with_qwen3(query, documents)
        elif isinstance(self.reranker_model, CrossEncoder):
            rerank_scores = self._rerank_with_crossencoder(query_doc_pairs)
        else:
            rerank_scores = self._rerank_with_similarity(query, documents)
        
        # Combine with medical relevance scores / 结合医疗相关性分数
        final_scores = self._combine_scores(query, documents, rerank_scores)
        
        # Sort by final scores / 按最终分数排序
        scored_docs = list(zip(documents, final_scores))
        scored_docs.sort(key=lambda x: x[1], reverse=True)
        
        # Return top-k reranked documents / 返回前k个重排序文档
        reranked_docs = []
        for doc, score in scored_docs[:top_k]:
            doc_copy = doc.copy()
            doc_copy["rerank_score"] = score
            reranked_docs.append(doc_copy)
        
        logger.info(f"Reranking completed, returned {len(reranked_docs)} documents")
        return reranked_docs
    
    def _extract_document_text(self, doc: Dict[str, Any]) -> str:
        """
        Extract text from document for reranking / 从文档中提取用于重排序的文本
        
        Args:
            doc: Document dictionary / 文档字典
            
        Returns:
            Document text / 文档文本
        """
        # Prioritize different text fields / 优先考虑不同的文本字段
        text_fields = ["text", "response", "query", "content"]
        
        for field in text_fields:
            if field in doc and doc[field]:
                return str(doc[field])
        
        # Fallback to concatenating available fields / 回退到连接可用字段
        text_parts = []
        if "query" in doc and doc["query"]:
            text_parts.append(f"问题: {doc['query']}")
        if "response" in doc and doc["response"]:
            text_parts.append(f"回答: {doc['response']}")
        
        return " ".join(text_parts) if text_parts else ""
    
    def _rerank_with_qwen3(self, query: str, documents: List[Dict[str, Any]]) -> List[float]:
        """
        Rerank using Qwen3 reranker model / 使用Qwen3重排序模型重排序

        Args:
            query: Search query / 搜索查询
            documents: List of documents / 文档列表

        Returns:
            Reranking scores / 重排序分数
        """
        try:
            scores = []
            max_length = 8192  # Qwen3 context length

            for doc in documents:
                doc_text = self._extract_document_text(doc)

                # Format instruction for Qwen3 reranker / 为Qwen3重排序器格式化指令
                instruction = self.instruction_template if self.use_instruction else "Given a web search query, retrieve relevant passages that answer the query"
                formatted_input = f"<Instruct>: {instruction}\n<Query>: {query}\n<Document>: {doc_text}"

                # Tokenize input / 标记化输入
                inputs = self.tokenizer(
                    [formatted_input],
                    padding=False,
                    truncation='longest_first',
                    return_attention_mask=False,
                    max_length=max_length - len(self.prefix_tokens) - len(self.suffix_tokens)
                )

                # Add prefix and suffix tokens / 添加前缀和后缀token
                input_ids = self.prefix_tokens + inputs['input_ids'][0] + self.suffix_tokens

                # Pad and convert to tensor / 填充并转换为张量
                inputs = self.tokenizer.pad(
                    {'input_ids': [input_ids]},
                    padding=True,
                    return_tensors="pt",
                    max_length=max_length
                )

                # Move to device / 移动到设备
                for key in inputs:
                    inputs[key] = inputs[key].to(self.reranker_model.device)

                # Get logits / 获取logits
                with torch.no_grad():
                    outputs = self.reranker_model(**inputs)
                    logits = outputs.logits[:, -1, :]

                    # Get true/false probabilities / 获取真/假概率
                    true_logit = logits[:, self.token_true_id]
                    false_logit = logits[:, self.token_false_id]

                    # Calculate probability / 计算概率
                    probs = torch.stack([false_logit, true_logit], dim=1)
                    probs = torch.nn.functional.log_softmax(probs, dim=1)
                    score = probs[:, 1].exp().item()

                    scores.append(score)

            return scores

        except Exception as e:
            logger.error(f"Error in Qwen3 reranking: {e}")
            # Fallback to similarity-based reranking / 回退到基于相似性的重排序
            return [0.5] * len(documents)

    def _rerank_with_crossencoder(self, query_doc_pairs: List[List[str]]) -> List[float]:
        """
        Rerank using CrossEncoder model / 使用CrossEncoder模型重排序

        Args:
            query_doc_pairs: List of [query, document] pairs / [查询, 文档]对列表

        Returns:
            Reranking scores / 重排序分数
        """
        try:
            scores = self.reranker_model.predict(query_doc_pairs, batch_size=self.batch_size)
            return scores.tolist() if hasattr(scores, 'tolist') else list(scores)
        except Exception as e:
            logger.error(f"Error in CrossEncoder reranking: {e}")
            # Fallback to similarity-based reranking / 回退到基于相似性的重排序
            return [0.5] * len(query_doc_pairs)
    
    def _rerank_with_similarity(self, query: str, documents: List[Dict[str, Any]]) -> List[float]:
        """
        Rerank using similarity-based approach / 使用基于相似性的方法重排序
        
        Args:
            query: Search query / 搜索查询
            documents: List of documents / 文档列表
            
        Returns:
            Similarity scores / 相似性分数
        """
        scores = []
        for doc in documents:
            doc_text = self._extract_document_text(doc)
            similarity = calculate_text_similarity(query, doc_text)
            scores.append(similarity)
        
        return scores
    
    def _combine_scores(self, query: str, documents: List[Dict[str, Any]], 
                       rerank_scores: List[float]) -> List[float]:
        """
        Combine reranking scores with medical relevance scores / 结合重排序分数和医疗相关性分数
        
        Args:
            query: Search query / 搜索查询
            documents: List of documents / 文档列表
            rerank_scores: Reranking scores / 重排序分数
            
        Returns:
            Combined final scores / 结合后的最终分数
        """
        final_scores = []
        
        for i, (doc, rerank_score) in enumerate(zip(documents, rerank_scores)):
            # Get original retrieval score / 获取原始检索分数
            retrieval_score = doc.get("score", 0.0)
            
            # Calculate medical relevance score / 计算医疗相关性分数
            medical_score = self._calculate_medical_relevance(query, doc)
            
            # Combine scores with weights / 使用权重结合分数
            # 40% rerank score, 30% retrieval score, 30% medical relevance
            final_score = (
                0.4 * rerank_score + 
                0.3 * retrieval_score + 
                0.3 * medical_score
            )
            
            final_scores.append(final_score)
        
        return final_scores
    
    def _calculate_medical_relevance(self, query: str, doc: Dict[str, Any]) -> float:
        """
        Calculate medical relevance score / 计算医疗相关性分数
        
        Args:
            query: Search query / 搜索查询
            doc: Document / 文档
            
        Returns:
            Medical relevance score / 医疗相关性分数
        """
        score = 0.0
        
        # Extract medical entities from query and document / 从查询和文档中提取医疗实体
        query_entities = extract_medical_entities(query)
        doc_entities = doc.get("medical_entities", {})
        
        # Score based on matching medical entities / 基于匹配的医疗实体评分
        for entity_type in ["symptoms", "diseases", "treatments"]:
            query_set = set(query_entities.get(entity_type, []))
            doc_set = set(doc_entities.get(entity_type, []))
            
            if query_set and doc_set:
                overlap = len(query_set.intersection(doc_set))
                total = len(query_set.union(doc_set))
                if total > 0:
                    score += (overlap / total) * 0.3  # Each entity type contributes 30%
        
        # Bonus for question type matching / 问题类型匹配奖励
        doc_metadata = doc.get("metadata", {})
        if "question_type" in doc_metadata:
            # Simple heuristic for question type matching / 问题类型匹配的简单启发式
            if any(keyword in query.lower() for keyword in ["怎么办", "如何治疗", "治疗"]):
                if "treatment" in doc_metadata.get("question_type", ""):
                    score += 0.1
        
        return min(score, 1.0)  # Cap at 1.0
    
    def batch_rerank(self, queries: List[str], 
                    documents_list: List[List[Dict[str, Any]]], 
                    top_k: int = None) -> List[List[Dict[str, Any]]]:
        """
        Batch rerank multiple query-document sets / 批量重排序多个查询-文档集
        
        Args:
            queries: List of search queries / 搜索查询列表
            documents_list: List of document lists for each query / 每个查询的文档列表
            top_k: Number of top documents to return for each query / 每个查询返回的顶部文档数量
            
        Returns:
            List of reranked document lists / 重排序后的文档列表
        """
        if len(queries) != len(documents_list):
            raise ValueError("Number of queries must match number of document lists")
        
        reranked_results = []
        for query, documents in zip(queries, documents_list):
            reranked_docs = self.rerank(query, documents, top_k)
            reranked_results.append(reranked_docs)
        
        return reranked_results
    
    def get_reranker_info(self) -> Dict[str, Any]:
        """
        Get reranker information / 获取重排序器信息
        
        Returns:
            Reranker information / 重排序器信息
        """
        return {
            "model_name": self.reranker_model_name,
            "model_type": type(self.reranker_model).__name__ if self.reranker_model else "Not loaded",
            "device": str(self.device),
            "top_k_rerank": self.top_k_rerank,
            "batch_size": self.batch_size,
            "enabled": self.reranking_config.get("enabled", True)
        }
