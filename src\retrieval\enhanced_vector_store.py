# -*- coding: utf-8 -*-
"""
Enhanced Vector Store Module / 增强向量存储模块

This module implements an enhanced FAISS-based vector storage with medical-specific features.
该模块实现增强的基于FAISS的向量存储，具有医疗特定功能。
"""

import os
import numpy as np
import faiss
import pickle
import json
import torch
from typing import List, Dict, Tuple, Optional, Any
from pathlib import Path
from sentence_transformers import SentenceTransformer

# Fix for transformers NoneType error / 修复transformers NoneType错误
os.environ.setdefault("TOKENIZERS_PARALLELISM", "false")
os.environ.setdefault("HF_HUB_DISABLE_PROGRESS_BARS", "1")

def _patch_transformers():
    """Patch transformers to fix NoneType error / 修补transformers修复NoneType错误"""
    try:
        import transformers
        from transformers import modeling_utils

        original_post_init = modeling_utils.PreTrainedModel.post_init

        def safe_post_init(self):
            try:
                if hasattr(self.config, 'fsdp_config') and self.config.fsdp_config is None:
                    self.config.fsdp_config = {}
                return original_post_init(self)
            except TypeError as e:
                if "argument of type 'NoneType' is not iterable" in str(e):
                    if hasattr(self.config, 'fsdp_config'):
                        if self.config.fsdp_config is None:
                            self.config.fsdp_config = {}
                    return
                raise e

        modeling_utils.PreTrainedModel.post_init = safe_post_init
        return True
    except:
        return False

# Apply patch automatically / 自动应用补丁
_patch_transformers()

# Try to import GPU FAISS
try:
    import faiss.contrib.torch_utils
    HAS_FAISS_GPU = True
except ImportError:
    HAS_FAISS_GPU = False
    
try:
    from ..utils import get_logger, get_config_manager, batch_process
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from utils import get_logger, get_config_manager, batch_process

logger = get_logger(__name__)


class MedicalVectorStore:
    """Enhanced medical vector store using FAISS / 使用FAISS的增强医疗向量存储"""
    
    def __init__(self, embedding_model: str = None, dimension: int = None):
        """
        Initialize FAISS vector store / 初始化FAISS向量存储
        
        Args:
            embedding_model: Name of the embedding model / 嵌入模型名称
            dimension: Dimension of the embeddings / 嵌入维度
        """
        # Get config from global config manager / 从全局配置管理器获取配置
        config_manager = get_config_manager()
        retrieval_config = config_manager.retrieval
        
        self.embedding_model_name = embedding_model or retrieval_config.embedding["model_name"]
        self.dimension = dimension or retrieval_config.vector_db["dimension"]
        self.batch_size = retrieval_config.embedding.get("batch_size", 32)
        self.normalize_embeddings = retrieval_config.embedding.get("normalize_embeddings", True)

        # GPU配置 / GPU Configuration
        self.use_gpu = retrieval_config.vector_db.get("use_gpu", "auto")
        self.gpu_device = retrieval_config.vector_db.get("gpu_device", 0)
        self.device = self._determine_device()

        # Qwen3特有配置 / Qwen3 specific configuration
        self.use_instruction = retrieval_config.embedding.get("use_instruction", False)
        self.instruction_template = retrieval_config.embedding.get("instruction_template", "")

        self.embedding_model = None
        self.index = None
        self.documents = []
        self.metadata = []

        # Custom Qwen3 implementation attributes / 自定义Qwen3实现属性
        self.is_custom_qwen3 = False
        self.qwen3_tokenizer = None
        self.qwen3_model = None

        logger.info(f"Initialized vector store with model: {self.embedding_model_name}")
        logger.info(f"Vector dimension: {self.dimension}")
        logger.info(f"Device: {self.device}")
        if self.use_instruction:
            logger.info(f"Using instruction template: {self.instruction_template}")

    def _determine_device(self) -> str:
        """
        Determine the device to use for FAISS / 确定FAISS使用的设备

        Returns:
            Device string ("cpu" or "gpu") / 设备字符串
        """
        if self.use_gpu == "cpu":
            return "cpu"
        elif self.use_gpu == "gpu":
            if torch.cuda.is_available() and HAS_FAISS_GPU:
                return "gpu"
            else:
                logger.warning("GPU requested but not available, falling back to CPU")
                return "cpu"
        else:  # auto
            if torch.cuda.is_available() and HAS_FAISS_GPU:
                logger.info("GPU detected and available, using GPU acceleration")
                return "gpu"
            else:
                logger.info("Using CPU for FAISS operations")
                return "cpu"
        
    def load_embedding_model(self):
        """Load the embedding model / 加载嵌入模型"""
        logger.info(f"Loading embedding model: {self.embedding_model_name}")

        try:
            # Check if it's a Qwen3 model / 检查是否为Qwen3模型
            if "qwen3" in self.embedding_model_name.lower():
                # Try SentenceTransformer first with proper configuration / 首先尝试使用正确配置的SentenceTransformer
                try:
                    model_kwargs = {
                        "trust_remote_code": True,
                        "attn_implementation": "eager"  # Disable flash attention
                    }

                    if self.device == "gpu":
                        model_kwargs["device_map"] = "auto"
                        model_kwargs["torch_dtype"] = "auto"

                    self.embedding_model = SentenceTransformer(
                        self.embedding_model_name,
                        model_kwargs=model_kwargs,
                        tokenizer_kwargs={"padding_side": "left", "trust_remote_code": True}
                    )
                    logger.info("Loaded Qwen3 embedding model with SentenceTransformer")

                except Exception as e1:
                    logger.warning(f"SentenceTransformer loading failed: {e1}")
                    # Fallback to custom Transformers implementation / 回退到自定义Transformers实现
                    self._load_qwen3_with_transformers()
                    logger.info("Loaded Qwen3 embedding model with custom Transformers")

            else:
                # Fallback to SentenceTransformers / 回退到SentenceTransformers
                device_map = "auto" if self.device == "gpu" else None
                model_kwargs = {"device_map": device_map} if device_map else {}

                self.embedding_model = SentenceTransformer(
                    self.embedding_model_name,
                    model_kwargs=model_kwargs
                )
                logger.info("Loaded SentenceTransformer model")

        except Exception as e:
            logger.warning(f"Failed to load with specific configuration: {e}")
            # Try with minimal configuration / 尝试使用最小配置
            try:
                self.embedding_model = SentenceTransformer(
                    self.embedding_model_name,
                    trust_remote_code=True
                )
                logger.info("Loaded model with basic configuration")
            except Exception as e2:
                logger.error(f"Failed to load model even with basic configuration: {e2}")
                # Final fallback to a working model / 最终回退到可工作的模型
                logger.warning("Using fallback embedding model")
                self.embedding_model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")
                self.embedding_model_name = "sentence-transformers/all-MiniLM-L6-v2"

        logger.info("Embedding model loaded successfully")

    def _load_qwen3_with_transformers(self):
        """Load Qwen3 model using custom Transformers implementation / 使用自定义Transformers实现加载Qwen3模型"""
        try:
            import os
            from transformers import AutoTokenizer, AutoModel
            import torch.nn.functional as F

            # Set environment variables to fix NoneType error / 设置环境变量修复NoneType错误
            os.environ.setdefault("TOKENIZERS_PARALLELISM", "false")
            os.environ.setdefault("HF_HUB_DISABLE_PROGRESS_BARS", "1")

            # Load tokenizer and model / 加载分词器和模型
            self.qwen3_tokenizer = AutoTokenizer.from_pretrained(
                self.embedding_model_name,
                padding_side='left',
                trust_remote_code=True,
                use_fast=False  # Use slow tokenizer to avoid issues
            )

            model_kwargs = {
                "trust_remote_code": True,
                "attn_implementation": "eager",  # Disable flash attention
                "low_cpu_mem_usage": True
            }

            if self.device == "gpu":
                model_kwargs["device_map"] = "auto"
                model_kwargs["torch_dtype"] = torch.float16
            else:
                model_kwargs["torch_dtype"] = torch.float32

            self.qwen3_model = AutoModel.from_pretrained(
                self.embedding_model_name,
                **model_kwargs
            )

            # Mark as custom Qwen3 implementation / 标记为自定义Qwen3实现
            self.is_custom_qwen3 = True

        except Exception as e:
            logger.error(f"Failed to load Qwen3 with Transformers: {e}")
            raise

    def _last_token_pool(self, last_hidden_states, attention_mask):
        """Last token pooling for Qwen3 / Qwen3的最后token池化"""
        import torch

        left_padding = (attention_mask[:, -1].sum() == attention_mask.shape[0])
        if left_padding:
            return last_hidden_states[:, -1]
        else:
            sequence_lengths = attention_mask.sum(dim=1) - 1
            batch_size = last_hidden_states.shape[0]
            return last_hidden_states[torch.arange(batch_size, device=last_hidden_states.device), sequence_lengths]

    def _get_detailed_instruct(self, task_description: str, query: str) -> str:
        """Format instruction for Qwen3 / 为Qwen3格式化指令"""
        return f'Instruct: {task_description}\nQuery: {query}'

    def create_index(self, index_type: str = None):
        """
        Create FAISS index / 创建FAISS索引

        Args:
            index_type: Type of FAISS index to create / 要创建的FAISS索引类型
        """
        if index_type is None:
            config_manager = get_config_manager()
            index_type = config_manager.retrieval.vector_db.get("index_type", "IndexFlatIP")

        logger.info(f"Creating FAISS index: {index_type} on {self.device}")

        # Create CPU index first / 首先创建CPU索引
        if index_type == "IndexFlatIP":
            cpu_index = faiss.IndexFlatIP(self.dimension)
        elif index_type == "IndexFlatL2":
            cpu_index = faiss.IndexFlatL2(self.dimension)
        elif index_type == "IndexIVFFlat":
            quantizer = faiss.IndexFlatL2(self.dimension)
            cpu_index = faiss.IndexIVFFlat(quantizer, self.dimension, 100)
        elif index_type == "IndexHNSWFlat":
            cpu_index = faiss.IndexHNSWFlat(self.dimension, 32)
        else:
            raise ValueError(f"Unsupported index type: {index_type}")

        # Move to GPU if requested and available / 如果请求且可用，则移动到GPU
        if self.device == "gpu" and HAS_FAISS_GPU:
            try:
                # Create GPU resource / 创建GPU资源
                res = faiss.StandardGpuResources()

                # Configure GPU index / 配置GPU索引
                gpu_config = faiss.GpuIndexFlatConfig()
                gpu_config.device = self.gpu_device

                # Move index to GPU / 将索引移动到GPU
                if index_type == "IndexFlatIP":
                    self.index = faiss.GpuIndexFlatIP(res, self.dimension, gpu_config)
                elif index_type == "IndexFlatL2":
                    self.index = faiss.GpuIndexFlatL2(res, self.dimension, gpu_config)
                else:
                    # For other index types, use CPU-GPU hybrid / 对于其他索引类型，使用CPU-GPU混合
                    self.index = faiss.index_cpu_to_gpu(res, self.gpu_device, cpu_index)

                logger.info(f"FAISS index created successfully on GPU {self.gpu_device}")
            except Exception as e:
                logger.warning(f"Failed to create GPU index, falling back to CPU: {e}")
                self.index = cpu_index
                logger.info("FAISS index created successfully on CPU (fallback)")
        else:
            self.index = cpu_index
            logger.info("FAISS index created successfully on CPU")
    
    def encode_texts(self, texts: List[str], is_query: bool = False) -> np.ndarray:
        """
        Encode texts to embeddings / 将文本编码为嵌入

        Args:
            texts: List of texts to encode / 要编码的文本列表
            is_query: Whether the texts are queries (for instruction prompting) / 文本是否为查询（用于指令提示）

        Returns:
            Embeddings array / 嵌入数组
        """
        try:
            # Filter out empty texts / 过滤空文本
            non_empty_texts = [text for text in texts if text and text.strip()]
            if len(non_empty_texts) != len(texts):
                logger.warning(f"Found {len(texts) - len(non_empty_texts)} empty texts, using placeholder")
                # Replace empty texts with placeholder / 用占位符替换空文本
                processed_texts = [text if text and text.strip() else "空文本" for text in texts]
            else:
                processed_texts = texts

            # Check if using custom Qwen3 implementation / 检查是否使用自定义Qwen3实现
            if hasattr(self, 'is_custom_qwen3') and self.is_custom_qwen3:
                return self._encode_with_custom_qwen3(processed_texts, is_query)

            # Apply instruction template for Qwen3 models if enabled / 如果启用，为Qwen3模型应用指令模板
            if self.use_instruction and is_query and self.instruction_template and "qwen3" in self.embedding_model_name.lower():
                # Add instruction prefix for queries / 为查询添加指令前缀
                processed_texts = [f"{self.instruction_template} {text}" for text in processed_texts]
                logger.debug(f"Applied instruction template to {len(processed_texts)} query texts")

            # Use SentenceTransformer / 使用SentenceTransformer
            logger.debug(f"Using SentenceTransformer to encode {len(processed_texts)} texts")

            # For Qwen3 models, use prompt_name if available / 对于Qwen3模型，如果可用则使用prompt_name
            encode_kwargs = {
                "normalize_embeddings": self.normalize_embeddings,
                "batch_size": min(self.batch_size, len(processed_texts)),
                "show_progress_bar": False  # Disable progress bar to avoid clutter
            }

            # Add prompt_name for Qwen3 queries / 为Qwen3查询添加prompt_name
            if "qwen3" in self.embedding_model_name.lower() and is_query:
                try:
                    encode_kwargs["prompt_name"] = "query"
                except:
                    # If prompt_name is not supported, continue without it / 如果不支持prompt_name，则继续不使用它
                    pass

            embeddings = self.embedding_model.encode(processed_texts, **encode_kwargs)

            return embeddings.astype(np.float32)

        except Exception as e:
            logger.error(f"Error encoding texts: {e}")
            logger.error(f"Text sample: {texts[0][:100] if texts else 'No texts'}")
            raise

    def _encode_with_custom_qwen3(self, texts: List[str], is_query: bool = False) -> np.ndarray:
        """Encode texts using custom Qwen3 implementation / 使用自定义Qwen3实现编码文本"""
        import torch
        import torch.nn.functional as F

        try:
            # Prepare input texts / 准备输入文本
            input_texts = []
            for text in texts:
                if is_query and self.use_instruction and self.instruction_template:
                    # Apply instruction template for queries / 为查询应用指令模板
                    formatted_text = self._get_detailed_instruct(self.instruction_template, text)
                    input_texts.append(formatted_text)
                else:
                    input_texts.append(text)

            max_length = 8192  # Qwen3 context length

            # Tokenize the input texts / 标记化输入文本
            batch_dict = self.qwen3_tokenizer(
                input_texts,
                padding=True,
                truncation=True,
                max_length=max_length,
                return_tensors="pt",
            )

            # Move to device / 移动到设备
            batch_dict = {k: v.to(self.qwen3_model.device) for k, v in batch_dict.items()}

            # Get embeddings / 获取嵌入
            with torch.no_grad():
                outputs = self.qwen3_model(**batch_dict)
                embeddings = self._last_token_pool(outputs.last_hidden_state, batch_dict['attention_mask'])

                # Normalize embeddings / 标准化嵌入
                if self.normalize_embeddings:
                    embeddings = F.normalize(embeddings, p=2, dim=1)

            return embeddings.cpu().numpy().astype(np.float32)

        except Exception as e:
            logger.error(f"Error in custom Qwen3 encoding: {e}")
            raise

    def add_documents(self, documents: List[Dict], batch_size: int = None):
        """
        Add documents to the vector store / 向向量存储添加文档

        Args:
            documents: List of document dictionaries / 文档字典列表
            batch_size: Batch size for processing / 处理批次大小
        """
        if self.embedding_model is None:
            self.load_embedding_model()

        batch_size = batch_size or self.batch_size
        logger.info(f"Adding {len(documents)} documents to vector store in batches of {batch_size}")

        # Process documents in batches / 批量处理文档
        first_batch = True
        total_processed = 0

        try:
            for batch_idx, batch in enumerate(batch_process(documents, batch_size)):
                logger.info(f"Processing batch {batch_idx + 1}/{(len(documents) + batch_size - 1) // batch_size}")

                # Extract texts for embedding / 提取文本用于嵌入
                texts = [doc.get("text", "") for doc in batch]

                # Skip empty texts / 跳过空文本
                if not any(texts):
                    logger.warning(f"Batch {batch_idx + 1} contains only empty texts, skipping...")
                    continue

                # Generate embeddings / 生成嵌入
                logger.debug(f"Generating embeddings for {len(texts)} texts...")
                embeddings = self.encode_texts(texts)
                logger.debug(f"Generated embeddings with shape: {embeddings.shape}")

                # Create index with correct dimension on first batch / 在第一批时使用正确的维度创建索引
                if first_batch:
                    actual_dimension = embeddings.shape[1]
                    if self.dimension != actual_dimension:
                        logger.warning(f"Updating dimension from {self.dimension} to {actual_dimension}")
                        self.dimension = actual_dimension

                    if self.index is None:
                        self.create_index()
                    first_batch = False

                # Add to FAISS index / 添加到FAISS索引
                logger.debug(f"Adding {len(embeddings)} embeddings to FAISS index...")
                self.index.add(embeddings)

                # Store documents and metadata / 存储文档和元数据
                self.documents.extend(batch)
                self.metadata.extend([doc.get("metadata", {}) for doc in batch])

                total_processed += len(batch)
                logger.info(f"Processed {total_processed}/{len(documents)} documents")

                # Force garbage collection to free memory / 强制垃圾回收以释放内存
                import gc
                gc.collect()

        except Exception as e:
            logger.error(f"Error processing batch {batch_idx + 1}: {e}")
            raise

        logger.info(f"Successfully added {len(documents)} documents. Total: {len(self.documents)}")
    
    def search(self, query: str, top_k: int = None, 
               similarity_threshold: float = None) -> List[Tuple[Dict, float]]:
        """
        Search for similar documents / 搜索相似文档
        
        Args:
            query: Search query / 搜索查询
            top_k: Number of top results to return / 返回的顶部结果数量
            similarity_threshold: Minimum similarity threshold / 最小相似度阈值
            
        Returns:
            List of (document, score) tuples / (文档, 分数)元组列表
        """
        if self.embedding_model is None or self.index is None:
            raise ValueError("Vector store not initialized. Add documents first.")
        
        # Get default values from config / 从配置获取默认值
        config_manager = get_config_manager()
        search_config = config_manager.retrieval.search
        top_k = top_k or search_config.get("top_k", 10)
        similarity_threshold = similarity_threshold or search_config.get("similarity_threshold", 0.7)
        
        # Generate query embedding / 生成查询嵌入
        query_embedding = self.encode_texts([query], is_query=True)
        
        # Search in FAISS index / 在FAISS索引中搜索
        scores, indices = self.index.search(query_embedding, top_k)
        
        # Prepare results / 准备结果
        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx < len(self.documents) and score >= similarity_threshold:
                results.append((self.documents[idx], float(score)))
        
        logger.debug(f"Found {len(results)} documents above threshold {similarity_threshold}")
        return results
    
    def save(self, save_path: str):
        """
        Save the vector store to disk / 保存向量存储到磁盘
        
        Args:
            save_path: Path to save the vector store / 保存向量存储的路径
        """
        save_path = Path(save_path)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # Save FAISS index / 保存FAISS索引
        faiss.write_index(self.index, str(save_path / "index.faiss"))
        
        # Save documents and metadata / 保存文档和元数据
        with open(save_path / "documents.pkl", "wb") as f:
            pickle.dump(self.documents, f)
        
        with open(save_path / "metadata.pkl", "wb") as f:
            pickle.dump(self.metadata, f)
        
        # Save configuration / 保存配置
        config = {
            "embedding_model": self.embedding_model_name,
            "dimension": self.dimension,
            "num_documents": len(self.documents)
        }
        
        with open(save_path / "config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Vector store saved to {save_path}")
    
    def load(self, load_path: str):
        """
        Load the vector store from disk / 从磁盘加载向量存储
        
        Args:
            load_path: Path to load the vector store from / 加载向量存储的路径
        """
        load_path = Path(load_path)
        
        if not load_path.exists():
            raise FileNotFoundError(f"Vector store not found at {load_path}")
        
        # Load configuration / 加载配置
        with open(load_path / "config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        self.embedding_model_name = config["embedding_model"]
        self.dimension = config["dimension"]
        
        # Load embedding model / 加载嵌入模型
        self.load_embedding_model()
        
        # Load FAISS index / 加载FAISS索引
        self.index = faiss.read_index(str(load_path / "index.faiss"))
        
        # Load documents and metadata / 加载文档和元数据
        with open(load_path / "documents.pkl", "rb") as f:
            self.documents = pickle.load(f)
        
        with open(load_path / "metadata.pkl", "rb") as f:
            self.metadata = pickle.load(f)
        
        logger.info(f"Vector store loaded from {load_path}")
        logger.info(f"Loaded {len(self.documents)} documents")


def create_vector_store_from_knowledge_base(
    knowledge_base_path: str, 
    vector_store_path: str,
    embedding_model: str = None
) -> MedicalVectorStore:
    """
    Create vector store from knowledge base file / 从知识库文件创建向量存储
    
    Args:
        knowledge_base_path: Path to knowledge base JSONL file / 知识库JSONL文件路径
        vector_store_path: Path to save vector store / 保存向量存储的路径
        embedding_model: Embedding model to use / 要使用的嵌入模型
        
    Returns:
        Created vector store / 创建的向量存储
    """
    # Load knowledge base / 加载知识库
    documents = []
    with open(knowledge_base_path, "r", encoding="utf-8") as f:
        for line in f:
            documents.append(json.loads(line.strip()))
    
    # Create vector store / 创建向量存储
    vector_store = MedicalVectorStore(embedding_model=embedding_model)
    vector_store.add_documents(documents)
    vector_store.save(vector_store_path)
    
    logger.info(f"Created vector store with {len(documents)} documents")
    return vector_store
