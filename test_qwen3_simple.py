#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Qwen3 Test Script / 简单Qwen3测试脚本

This script provides a minimal test for Qwen3 models without complex dependencies.
该脚本为Qwen3模型提供最小化测试，无复杂依赖。

Usage / 使用方法:
  python test_qwen3_simple.py
"""

import os
import sys

def setup_environment():
    """Setup environment variables / 设置环境变量"""
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    os.environ["HF_HUB_DISABLE_PROGRESS_BARS"] = "1"
    os.environ["TRANSFORMERS_OFFLINE"] = "0"
    os.environ["HF_HUB_OFFLINE"] = "0"

def test_basic_imports():
    """Test basic imports / 测试基本导入"""
    print("🧪 Testing basic imports...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"   CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   GPU: {torch.cuda.get_device_name(0)}")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        import transformers
        print(f"✅ Transformers: {transformers.__version__}")
    except ImportError as e:
        print(f"❌ Transformers import failed: {e}")
        return False
    
    try:
        from sentence_transformers import SentenceTransformer
        print("✅ SentenceTransformers imported")
    except ImportError as e:
        print(f"❌ SentenceTransformers import failed: {e}")
        return False
    
    return True

def test_qwen3_embedding_simple():
    """Test Qwen3 embedding with minimal configuration / 使用最小配置测试Qwen3嵌入"""
    print("\n🧪 Testing Qwen3 Embedding (Simple)...")
    
    try:
        from transformers import AutoTokenizer, AutoModel
        import torch
        import torch.nn.functional as F
        
        model_name = "Qwen/Qwen3-Embedding-0.6B"
        
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            trust_remote_code=True,
            use_fast=False
        )
        print("✅ Tokenizer loaded")
        
        # Load model
        model = AutoModel.from_pretrained(
            model_name,
            trust_remote_code=True,
            attn_implementation="eager",
            torch_dtype=torch.float32,  # Use float32 for stability
            device_map="cpu"  # Force CPU to avoid GPU issues
        )
        print("✅ Model loaded")
        
        # Test encoding
        texts = ["Hello world", "This is a test"]
        inputs = tokenizer(texts, padding=True, truncation=True, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model(**inputs)
            # Simple mean pooling
            embeddings = outputs.last_hidden_state.mean(dim=1)
            embeddings = F.normalize(embeddings, p=2, dim=1)
        
        print(f"✅ Encoding successful - shape: {embeddings.shape}")
        return True
        
    except Exception as e:
        print(f"❌ Qwen3 embedding test failed: {e}")
        return False

def test_sentence_transformers_fallback():
    """Test with a known working SentenceTransformer model / 使用已知可工作的SentenceTransformer模型测试"""
    print("\n🧪 Testing SentenceTransformers fallback...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        # Use a simple, reliable model
        model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")
        
        texts = ["Hello world", "This is a test"]
        embeddings = model.encode(texts)
        
        print(f"✅ Fallback model working - shape: {embeddings.shape}")
        return True
        
    except Exception as e:
        print(f"❌ Fallback model test failed: {e}")
        return False

def test_qwen3_with_sentence_transformers():
    """Test Qwen3 with SentenceTransformers / 使用SentenceTransformers测试Qwen3"""
    print("\n🧪 Testing Qwen3 with SentenceTransformers...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        model_kwargs = {
            "trust_remote_code": True,
            "attn_implementation": "eager",
            "torch_dtype": "float32",
            "device_map": "cpu"
        }
        
        model = SentenceTransformer(
            "Qwen/Qwen3-Embedding-0.6B",
            model_kwargs=model_kwargs,
            tokenizer_kwargs={"trust_remote_code": True, "use_fast": False}
        )
        
        texts = ["Hello world", "This is a test"]
        embeddings = model.encode(texts, show_progress_bar=False)
        
        print(f"✅ SentenceTransformers + Qwen3 working - shape: {embeddings.shape}")
        return True
        
    except Exception as e:
        print(f"❌ SentenceTransformers + Qwen3 failed: {e}")
        return False

def main():
    """Main test function / 主测试函数"""
    print("🚀 Simple Qwen3 Test")
    print("=" * 40)
    
    # Setup environment
    setup_environment()
    
    # Run tests
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Qwen3 Embedding (Simple)", test_qwen3_embedding_simple),
        ("SentenceTransformers Fallback", test_sentence_transformers_fallback),
        ("Qwen3 + SentenceTransformers", test_qwen3_with_sentence_transformers)
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 Test Results")
    print("-" * 25)
    
    passed = 0
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed >= 2:  # At least basic imports and one model test
        print("\n🎉 Core functionality working!")
        print("You can proceed with the main application.")
    else:
        print("\n⚠️  Core functionality issues detected.")
        print("Please check your environment and dependencies.")
    
    return 0 if passed >= 2 else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
